#!/bin/bash

echo "========================================"
echo "    3D Kamera Mód pro Minecraft"
echo "========================================"
echo

# Kontrola Node.js
if ! command -v node &> /dev/null; then
    echo "❌ CHYBA: Node.js není nainstalovaný!"
    echo "Instalace: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -"
    echo "           sudo apt-get install -y nodejs"
    exit 1
fi

# Kontrola Python
if ! command -v python3 &> /dev/null; then
    echo "❌ CHYBA: Python3 není nainstalovaný!"
    echo "Instalace: sudo apt install python3 python3-pip"
    exit 1
fi

echo "✅ Node.js a Python jsou nainstalovány"
echo

# Kontrola závislostí
if [ ! -d "node_modules" ]; then
    echo "📦 Instaluji Node.js závislosti..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ CHYBA: Instalace Node.js závislostí selhala!"
        exit 1
    fi
fi

echo "✅ Závislosti jsou nainstalovány"
echo

# Funkce pro menu
show_menu() {
    echo "Vyberte akci:"
    echo "1. Spustit RCON Bridge"
    echo "2. Spustit Python server"
    echo "3. Spustit oba servery"
    echo "4. Test systému"
    echo "5. Minecraft setup"
    echo "6. Ukončit"
    echo
}

# Hlavní smyčka
while true; do
    show_menu
    read -p "Vaše volba (1-6): " choice
    
    case $choice in
        1)
            echo
            echo "🚀 Spouštím RCON Bridge..."
            echo "Stiskni Ctrl+C pro zastavení"
            echo
            node server.js
            ;;
        2)
            echo
            echo "🚀 Spouštím Python server..."
            echo "Stiskni Ctrl+C pro zastavení"
            echo
            python3 depth_person_server.py
            ;;
        3)
            echo
            echo "🚀 Spouštím oba servery..."
            echo "POZOR: Spustí nejdříve Bridge, pak Python server v novém terminálu"
            echo
            
            # Spuštění bridge v pozadí
            gnome-terminal -- bash -c "echo 'RCON Bridge'; node server.js; read -p 'Stiskni Enter pro ukončení...'"
            sleep 3
            
            # Spuštění Python serveru v novém terminálu
            gnome-terminal -- bash -c "echo 'Python Server'; python3 depth_person_server.py; read -p 'Stiskni Enter pro ukončení...'"
            
            echo "✅ Servery spuštěny v nových terminálech"
            ;;
        4)
            echo
            echo "🧪 Spouštím test systému..."
            python3 test_system.py
            echo
            read -p "Stiskni Enter pro pokračování..."
            ;;
        5)
            echo
            echo "🎮 Spouštím Minecraft setup..."
            node minecraft_setup.js
            echo
            read -p "Stiskni Enter pro pokračování..."
            ;;
        6)
            echo
            echo "👋 Nashledanou!"
            exit 0
            ;;
        *)
            echo "❌ Neplatná volba!"
            ;;
    esac
    
    echo
done
