#!/usr/bin/env node
/**
 * Pomocný script pro nastavení Minecraft prostoru pro 3D kamera mód
 * P<PERSON><PERSON><PERSON>j<PERSON> se k RCON a připraví prostor pro voxely
 */

import { Rcon } from 'rcon-client';
import readline from 'readline';

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise(resolve => rl.question(prompt, resolve));
}

class MinecraftSetup {
    constructor() {
        this.rcon = null;
    }

    async connect() {
        console.log('🔗 Připojování k Minecraft serveru...');
        
        const host = await question('RCON Host (127.0.0.1): ') || '127.0.0.1';
        const port = parseInt(await question('RCON Port (25575): ') || '25575');
        const password = await question('RCON Password (changeme): ') || 'changeme';
        
        try {
            this.rcon = await Rcon.connect({ host, port, password });
            console.log('✅ Připojeno k Minecraft serveru');
            return true;
        } catch (error) {
            console.error('❌ P<PERSON>ip<PERSON>jení selhalo:', error.message);
            return false;
        }
    }

    async setupVoxelSpace() {
        console.log('\n📦 Nastavuji voxel prostor 64×36×36...');
        
        // Získání pozice pro prostor
        const x = parseInt(await question('X pozice (0): ') || '0');
        const y = parseInt(await question('Y pozice (64): ') || '64');
        const z = parseInt(await question('Z pozice (0): ') || '0');
        
        const x1 = x + 63;  // 64 bloků šířka
        const y1 = y + 35;  // 36 bloků výška  
        const z1 = z + 35;  // 36 bloků hloubka
        
        try {
            // Vymazání prostoru
            console.log(`🧹 Mazání prostoru (${x},${y},${z}) -> (${x1},${y1},${z1})`);
            await this.rcon.send(`fill ${x} ${y} ${z} ${x1} ${y1} ${z1} air`);
            
            // Vytvoření rámečku pro orientaci (volitelné)
            const frame = await question('Vytvořit rámeček pro orientaci? (y/n): ');
            if (frame.toLowerCase() === 'y') {
                await this.createFrame(x, y, z, x1, y1, z1);
            }
            
            console.log('✅ Voxel prostor připraven!');
            
            // Teleportace hráče
            const tp = await question('Teleportovat se k prostoru? (y/n): ');
            if (tp.toLowerCase() === 'y') {
                const centerX = x + 32;
                const centerY = y + 50;
                const centerZ = z + 50;
                await this.rcon.send(`tp @p ${centerX} ${centerY} ${centerZ}`);
                console.log(`📍 Teleportováno na pozici (${centerX}, ${centerY}, ${centerZ})`);
            }
            
        } catch (error) {
            console.error('❌ Chyba při nastavování prostoru:', error.message);
        }
    }

    async createFrame(x, y, z, x1, y1, z1) {
        console.log('🖼️ Vytvářím orientační rámeček...');
        
        try {
            // Spodní rámeček (Y rovina)
            await this.rcon.send(`fill ${x} ${y-1} ${z} ${x1} ${y-1} ${z} white_concrete`);
            await this.rcon.send(`fill ${x} ${y-1} ${z1} ${x1} ${y-1} ${z1} white_concrete`);
            await this.rcon.send(`fill ${x} ${y-1} ${z} ${x} ${y-1} ${z1} white_concrete`);
            await this.rcon.send(`fill ${x1} ${y-1} ${z} ${x1} ${y-1} ${z1} white_concrete`);
            
            // Rohové sloupy
            await this.rcon.send(`fill ${x} ${y} ${z} ${x} ${y1} ${z} red_concrete`);
            await this.rcon.send(`fill ${x1} ${y} ${z} ${x1} ${y1} ${z} green_concrete`);
            await this.rcon.send(`fill ${x} ${y} ${z1} ${x} ${y1} ${z1} blue_concrete`);
            await this.rcon.send(`fill ${x1} ${y} ${z1} ${x1} ${y1} ${z1} yellow_concrete`);
            
            // Označení os
            await this.rcon.send(`setblock ${x+5} ${y-2} ${z} red_concrete`);  // X osa
            await this.rcon.send(`setblock ${x} ${y+5} ${z} green_concrete`); // Y osa  
            await this.rcon.send(`setblock ${x} ${y-2} ${z+5} blue_concrete`); // Z osa
            
            console.log('✅ Rámeček vytvořen:');
            console.log('  🔴 Červený roh: (0,0,0) - začátek X osy');
            console.log('  🟢 Zelený roh: (63,0,0) - konec X osy');
            console.log('  🔵 Modrý roh: (0,0,35) - konec Z osy');
            console.log('  🟡 Žlutý roh: (63,0,35) - konec X+Z os');
            
        } catch (error) {
            console.error('❌ Chyba při vytváření rámečku:', error.message);
        }
    }

    async testConnection() {
        console.log('\n🧪 Testování RCON připojení...');
        
        try {
            const response = await this.rcon.send('time query daytime');
            console.log('✅ RCON funguje, čas ve hře:', response);
            
            const tps = await this.rcon.send('forge tps');
            console.log('📊 Server TPS:', tps);
            
        } catch (error) {
            console.log('⚠️ Některé příkazy nejsou dostupné:', error.message);
        }
    }

    async showMenu() {
        console.log('\n🎮 Minecraft Setup Menu:');
        console.log('1. Nastavit voxel prostor');
        console.log('2. Testovat RCON připojení');
        console.log('3. Vymazat existující prostor');
        console.log('4. Ukázat příkazy pro manuální setup');
        console.log('5. Ukončit');
        
        const choice = await question('\nVyberte možnost (1-5): ');
        
        switch (choice) {
            case '1':
                await this.setupVoxelSpace();
                break;
            case '2':
                await this.testConnection();
                break;
            case '3':
                await this.clearSpace();
                break;
            case '4':
                this.showManualCommands();
                break;
            case '5':
                return false;
            default:
                console.log('❌ Neplatná volba');
        }
        
        return true;
    }

    async clearSpace() {
        console.log('\n🧹 Mazání prostoru...');
        
        const x = parseInt(await question('X pozice (0): ') || '0');
        const y = parseInt(await question('Y pozice (64): ') || '64');
        const z = parseInt(await question('Z pozice (0): ') || '0');
        
        const x1 = x + 63;
        const y1 = y + 35;
        const z1 = z + 35;
        
        try {
            await this.rcon.send(`fill ${x} ${y-2} ${z-2} ${x1+2} ${y1+2} ${z1+2} air`);
            console.log('✅ Prostor vymazán (včetně okolí)');
        } catch (error) {
            console.error('❌ Chyba při mazání:', error.message);
        }
    }

    showManualCommands() {
        console.log('\n📋 Manuální příkazy pro setup:');
        console.log('');
        console.log('# Základní prostor 64×36×36:');
        console.log('/fill 0 64 0 63 99 35 air');
        console.log('');
        console.log('# S rámečkem:');
        console.log('/fill 0 63 0 63 63 35 white_concrete');
        console.log('/setblock 0 64 0 red_concrete');
        console.log('/setblock 63 64 0 green_concrete');
        console.log('/setblock 0 64 35 blue_concrete');
        console.log('/setblock 63 64 35 yellow_concrete');
        console.log('');
        console.log('# Teleport do středu:');
        console.log('/tp @p 32 100 18');
        console.log('');
        console.log('# Gamemode pro lepší pohled:');
        console.log('/gamemode spectator');
        console.log('/gamemode creative');
    }

    async run() {
        console.log('🎮 Minecraft 3D Kamera Mód - Setup Tool');
        console.log('==========================================');
        
        if (!await this.connect()) {
            rl.close();
            return;
        }
        
        let continueMenu = true;
        while (continueMenu) {
            continueMenu = await this.showMenu();
        }
        
        if (this.rcon) {
            await this.rcon.end();
            console.log('👋 Odpojeno od serveru');
        }
        
        rl.close();
    }
}

// Spuštění
const setup = new MinecraftSetup();
setup.run().catch(console.error);
