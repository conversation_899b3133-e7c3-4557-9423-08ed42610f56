# 📱➡️🎮 3D Kamera Mód pro Minecraft

Systém pro real-time vytváření 3D voxelových soch v Minecraftu z mobilní kamery.

## 📁 Struktura projektu

```
3d-camera-minecraft/
├── 📱 camera_client.html      # Mobilní web klient
├── 🐍 depth_person_server.py  # Python AI server
├── 🌐 server.js               # Node.js RCON bridge
├── 📦 package.json            # Node.js závislosti
├── 📖 README.md               # Tento návod
├── 🧪 test_system.py          # Automatický test
├── 🎮 minecraft_setup.js      # Minecraft setup nástroj
├── 🪟 start_system.bat        # Windows start script
├── 🐧 start_system.sh         # Linux/Mac start script
└── 📝 .gitignore              # Git ignore soubor
```

## 🎯 Jak to funguje

1. **<PERSON>biln<PERSON> klient** (`camera_client.html`) - posílá snímky z kamery přes WebSocket
2. **Python server** (`depth_person_server.py`) - segmentace osoby + monokulární hloubka → 3D voxely
3. **Node.js bridge** (`server.js`) - RCON komunikace s Minecraft serverem

## 📋 Předpoklady

### Software
- **Node.js 18+** ([stáhnout](https://nodejs.org/))
- **Python 3.10+** ([stáhnout](https://www.python.org/))
- **Minecraft Java Edition** s RCON serverem

### Hardware
- **PC** s GPU (doporučeno) nebo silný CPU
- **Mobilní telefon** s kamerou a moderním prohlížečem
- **Lokální síť** (PC a mobil na stejné WiFi)

## 🚀 Rychlé spuštění

### Automatické spuštění (doporučeno)

**Windows:**
```cmd
start_system.bat
```

**Linux/Mac:**
```bash
./start_system.sh
```

Tyto scripty vás provedou celým procesem instalace a spuštění.

### Manuální spuštění

### 1. Minecraft server konfigurace

V `server.properties` nastavte:
```properties
enable-rcon=true
rcon.port=25575
rcon.password=changeme
```

**⚠️ Bezpečnost:** Změňte heslo na silnější a používejte pouze v lokální síti!

### 2. Instalace závislostí

#### Node.js bridge:
```bash
npm install
```

#### Python server:
```bash
pip install torch torchvision timm opencv-python numpy websockets mediapipe
```

### 3. Spuštění systému

#### Krok 1: Spusťte RCON bridge
```bash
npm start
```
Měli byste vidět: `🚀 RCON Bridge server spuštěn na ws://localhost:8787`

#### Krok 2: Spusťte Python server
```bash
python depth_person_server.py
```
Čekejte na: `✅ Server běží, čekám na připojení...`

#### Krok 3: Připravte prostor v Minecraftu
V Minecraft konzoli vytvořte prázdný prostor 64×36×36:
```
/fill 0 64 0 63 99 35 air
```

#### Krok 4: Mobilní klient
1. Otevřete `camera_client.html` na mobilu
2. Nastavte URL: `ws://IP_VAŠEHO_PC:9090` (zjistěte IP přes `ipconfig` nebo `ifconfig`)
3. Klikněte "🎥 Spustit kameru"
4. Povolte přístup ke kameře

## ⚙️ Konfigurace

### Orientace voxel prostoru

**Default (stěna):**
- `origin`: {x:0, y:64, z:0} - levý horní přední roh
- `right`: [1,0,0] - X osa (šířka)
- `down`: [0,-1,0] - Y osa dolů (výška)
- `forward`: [0,0,1] - Z osa (hloubka)

**Příklad podlahy:**
```python
ORIGIN = {"x": 0, "y": 64, "z": 0}
AXES = {"right": [1,0,0], "down": [0,0,1], "forward": [0,-1,0]}
```

### Laditelné parametry

V `depth_person_server.py`:
```python
MASK_THR = 0.5      # Práh segmentace (0.3-0.7)
THICK = 2           # Tloušťka sochy (1-4)
NEAR, FAR = 0.30, 2.70  # Hloubkové mapování
SMOOTH = 0.15       # Vyhlazení hloubky (0.1-0.3)
```

V `server.js` (throttling):
```javascript
maxPerTick: 800     # Max příkazů za tick (400-1200)
tps: 20            # Ticks per second (10-20)
```

## 🔧 Troubleshooting

### Nízký FPS / Lag
- **Snižte FPS** na mobilu (5-8 FPS)
- **Snižte maxPerTick** na 400-600
- **Použijte nižší rozlišení** (320×180)
- **CPU inference** je pomalejší než GPU

### "Žádná postava se nezobrazuje"
- **Zvyšte MASK_THR** na 0.6-0.7
- **Upravte NEAR/FAR** podle vzdálenosti od kamery
- **Zkontrolujte osvětlení** - segmentace potřebuje dobré světlo

### Připojení selhává
- **Firewall:** Povolte porty 8787, 9090
- **IP adresa:** Zkontrolujte správnou IP PC
- **RCON:** Ověřte heslo a port v Minecraft

### Server TPS klesá
- **Snižte maxPerTick** na 400
- **Snižte FPS** na mobilu
- **Zkontrolujte CPU/RAM** využití

## 📱 Mobilní klient - ovládání

- **FPS slider:** 1-30 snímků za sekundu
- **Rozlišení:** 320×180 (rychlé) až 1280×720 (kvalitní)
- **JPEG kvalita:** 0.1-1.0 (vyšší = větší soubory)
- **Statistiky:** Sledování připojení a výkonu

## 🎮 Minecraft příkazy

### Vymazání prostoru
```
/fill 0 64 0 63 99 35 air
```

### Teleport do pozice
```
/tp @p 32 100 18
```

### Gamemode pro lepší pohled
```
/gamemode spectator
```

## 🔒 Bezpečnost

- **RCON heslo:** Změňte z "changeme" na silné heslo
- **Lokální síť:** Používejte pouze v důvěryhodné síti
- **Firewall:** Omezte přístup k portům 8787, 9090
- **Backup světa:** Zálohujte před testováním

## 📊 Výkon

### Doporučené nastavení:
- **FPS:** 8-12 (mobil)
- **Rozlišení:** 640×360
- **maxPerTick:** 600-800
- **JPEG kvalita:** 0.7-0.8

### Minimální požadavky:
- **CPU:** 4 jádra, 2.5+ GHz
- **RAM:** 8 GB
- **GPU:** GTX 1060 / RX 580 (doporučeno)

## 🐛 Známé problémy

1. **První spuštění MiDaS** trvá déle (stahování modelu)
2. **MediaPipe** může mít problémy s některými telefony
3. **WebSocket** může být blokován některými sítěmi
4. **RCON timeout** při vysokém zatížení serveru

## 📚 Licence a modely

- **MiDaS:** [Intel ISL License](https://github.com/isl-org/MiDaS)
- **MediaPipe:** [Apache 2.0](https://github.com/google/mediapipe)
- **Tento projekt:** MIT License

## 🆘 Podpora

Při problémech zkontrolujte:
1. **Logy** v konzoli všech komponent
2. **Síťové připojení** mezi zařízeními
3. **Minecraft server** stav a TPS
4. **Python závislosti** verze

---

**Tip:** Pro nejlepší výsledky stůjte 1-2 metry od kamery s dobrým osvětlením pozadí!

## 🖥️ Instalace na Windows

### 1. Instalace Node.js
1. Stáhněte z [nodejs.org](https://nodejs.org/)
2. Spusťte installer a postupujte podle instrukcí
3. Ověřte: `node --version` a `npm --version`

### 2. Instalace Python
1. Stáhněte z [python.org](https://www.python.org/)
2. **Důležité:** Zaškrtněte "Add Python to PATH"
3. Ověřte: `python --version` a `pip --version`

### 3. Instalace závislostí
```cmd
# V adresáři projektu
npm install
pip install torch torchvision timm opencv-python numpy websockets mediapipe
```

### 4. Zjištění IP adresy
```cmd
ipconfig
```
Hledejte "IPv4 Address" pro vaši WiFi síť.

## 🐧 Instalace na Linux

### 1. Instalace Node.js (Ubuntu/Debian)
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. Instalace Python závislostí
```bash
sudo apt update
sudo apt install python3 python3-pip
pip3 install torch torchvision timm opencv-python numpy websockets mediapipe
```

### 3. Instalace projektu
```bash
npm install
```

### 4. Zjištění IP adresy
```bash
ip addr show | grep inet
```

## 🛠️ Pomocné nástroje

### Automatický test systému
```bash
python test_system.py
# nebo
npm run test
```
Otestuje všechny komponenty a závislosti.

### Minecraft setup nástroj
```bash
node minecraft_setup.js
# nebo
npm run setup
```
Interaktivní nástroj pro přípravu prostoru v Minecraftu.

### Start scripty
- `start_system.bat` (Windows) - Grafické menu pro spuštění
- `start_system.sh` (Linux/Mac) - Grafické menu pro spuštění

## 🧪 Testování systému

### Test 1: RCON Bridge
```bash
npm start
```
Očekávaný výstup:
```
🚀 RCON Bridge server spuštěn na ws://localhost:8787
```

### Test 2: Python Server
```bash
python depth_person_server.py
```
Očekávaný výstup:
```
🔄 Načítání modelů...
✅ MiDaS model načten
✅ MediaPipe segmentace načtena
🔗 Připojeno k RCON bridge
✅ Bridge konfigurace úspěšná
🚀 Spouštím WebSocket server na ws://0.0.0.0:9090
✅ Server běží, čekám na připojení...
```

### Test 3: Minecraft připojení
V Minecraft konzoli:
```
/rcon connect 127.0.0.1:25575 changeme
```

### Test 4: Mobilní klient
1. Otevřete `camera_client.html`
2. Nastavte správnou IP adresu
3. Klikněte "Spustit kameru"
4. Zkontrolujte status "Připojeno"

## 🎛️ Pokročilá konfigurace

### Preset orientací
V `depth_person_server.py` můžete přidat presets:

```python
ORIENTATIONS = {
    "wall_north": {"right": [1,0,0], "down": [0,-1,0], "forward": [0,0,1]},
    "wall_east": {"right": [0,0,1], "down": [0,-1,0], "forward": [-1,0,0]},
    "wall_south": {"right": [-1,0,0], "down": [0,-1,0], "forward": [0,0,-1]},
    "wall_west": {"right": [0,0,-1], "down": [0,-1,0], "forward": [1,0,0]},
    "floor": {"right": [1,0,0], "down": [0,0,1], "forward": [0,-1,0]}
}
```

### Vlastní barvy bloků
V `server.js` můžete upravit paletu:

```javascript
const CONCRETE_COLORS = {
    'white_concrete': [207, 213, 214],
    'orange_concrete': [224, 97, 1],
    // ... přidejte vlastní bloky
    'diamond_block': [93, 219, 213],
    'gold_block': [248, 198, 39]
};
```

## 🔄 Režim "Freeze"

Pro zmrazení aktuální sochy přidejte do mobilního klienta:

```javascript
// V camera_client.html, přidejte tlačítko
<button id="freezeBtn">❄️ Zmrazit</button>

// A handler
document.getElementById('freezeBtn').addEventListener('click', () => {
    this.isFrozen = !this.isFrozen;
    // Přestaň posílat snímky když je zmrazeno
});
```

## 📈 Optimalizace výkonu

### GPU vs CPU
```python
# Kontrola GPU v depth_person_server.py
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
else:
    print("Používám CPU - bude pomalejší")
```

### Monitoring výkonu
```bash
# CPU/RAM monitoring
htop  # Linux
taskmgr  # Windows

# GPU monitoring (NVIDIA)
nvidia-smi -l 1
```

### Batch optimalizace
V `server.js` můžete upravit batch zpracování:

```javascript
// Větší batche pro rychlejší zpracování
const BATCH_SIZE = 100;
const commandBatches = [];
for (let i = 0; i < commands.length; i += BATCH_SIZE) {
    commandBatches.push(commands.slice(i, i + BATCH_SIZE));
}
```
