@echo off
echo ========================================
echo    3D Kamera Mod pro Minecraft
echo ========================================
echo.

REM Kontrola Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: Node.js neni nainstalovany!
    echo Stahni z: https://nodejs.org/
    pause
    exit /b 1
)

REM Kontrola Python
python --version >nul 2>&1
if errorlevel 1 (
    echo CHYBA: Python neni nainstalovany!
    echo Stahni z: https://www.python.org/
    pause
    exit /b 1
)

echo ✅ Node.js a Python jsou nainstalovany
echo.

REM Kontrola zavislosti
if not exist "node_modules" (
    echo 📦 Instaluji Node.js zavislosti...
    npm install
    if errorlevel 1 (
        echo CHYBA: Instalace Node.js zavislosti selhala!
        pause
        exit /b 1
    )
)

echo ✅ Zavislosti jsou nainstalovany
echo.

REM Menu
:menu
echo Vyberte akci:
echo 1. Spustit RCON Bridge
echo 2. Spustit Python server
echo 3. Spustit oba servery
echo 4. Test systemu
echo 5. Minecraft setup
echo 6. Ukoncit
echo.
set /p choice="Vase volba (1-6): "

if "%choice%"=="1" goto bridge
if "%choice%"=="2" goto python
if "%choice%"=="3" goto both
if "%choice%"=="4" goto test
if "%choice%"=="5" goto setup
if "%choice%"=="6" goto end
echo Neplatna volba!
goto menu

:bridge
echo.
echo 🚀 Spoustim RCON Bridge...
echo Stiskni Ctrl+C pro zastaveni
echo.
node server.js
goto menu

:python
echo.
echo 🚀 Spoustim Python server...
echo Stiskni Ctrl+C pro zastaveni
echo.
python depth_person_server.py
goto menu

:both
echo.
echo 🚀 Spoustim oba servery...
echo POZOR: Spusti nejdrive Bridge, pak Python server v novem okne
echo.
start "RCON Bridge" cmd /k "node server.js"
timeout /t 3 /nobreak >nul
start "Python Server" cmd /k "python depth_person_server.py"
echo ✅ Servery spusteny v novych oknech
goto menu

:test
echo.
echo 🧪 Spoustim test systemu...
python test_system.py
echo.
pause
goto menu

:setup
echo.
echo 🎮 Spoustim Minecraft setup...
node minecraft_setup.js
echo.
pause
goto menu

:end
echo.
echo 👋 Nashledanou!
pause
