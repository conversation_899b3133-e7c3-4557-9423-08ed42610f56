{"name": "mc-rcon-voxel-bridge", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "mc-rcon-voxel-bridge", "version": "1.0.0", "license": "MIT", "dependencies": {"rcon-client": "^4.2.5", "ws": "^8.18.0"}}, "node_modules/rcon-client": {"version": "4.2.5", "resolved": "https://registry.npmjs.org/rcon-client/-/rcon-client-4.2.5.tgz", "integrity": "sha512-AnX1GU/ZTlwtYup3H6h0J1hwfP3OYltXVe+8ReBzmNEepX3xGH8nDg7gYqT5Y9rpAS/LmQ48h0BKINt1YGd8bA==", "license": "MIT", "dependencies": {"typed-emitter": "^0.1.0"}}, "node_modules/typed-emitter": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/typed-emitter/-/typed-emitter-0.1.0.tgz", "integrity": "sha512-Tfay0l6gJMP5rkil8CzGbLthukn+9BN/VXWcABVFPjOoelJ+koW8BuPZYk+h/L+lEeIp1fSzVRiWRPIjKVjPdg==", "license": "MIT"}, "node_modules/ws": {"version": "8.18.3", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.3.tgz", "integrity": "sha512-PEIGCY5tSlUt50cqyMXfCzX+oOPqN0vuGqWzbcJ2xvnkzkq46oOpz7dQaTDBdfICb4N14+GARUDw2XV2N4tvzg==", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}}