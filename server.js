import { WebSocketServer } from 'ws';
import { Rcon } from 'rcon-client';

// Konstanty pro voxel prostor
const W = 64, H = 36, D = 36;

// Barevná paleta concrete bloků (RGB hodnoty)
const CONCRETE_COLORS = {
    'white_concrete': [207, 213, 214],
    'orange_concrete': [224, 97, 1],
    'magenta_concrete': [169, 48, 159],
    'light_blue_concrete': [36, 137, 199],
    'yellow_concrete': [254, 216, 61],
    'lime_concrete': [94, 168, 24],
    'pink_concrete': [214, 101, 143],
    'gray_concrete': [54, 57, 61],
    'light_gray_concrete': [125, 125, 115],
    'cyan_concrete': [21, 119, 136],
    'purple_concrete': [100, 32, 156],
    'blue_concrete': [45, 47, 143],
    'brown_concrete': [96, 60, 32],
    'green_concrete': [73, 91, 36],
    'red_concrete': [142, 33, 23],
    'black_concrete': [8, 10, 15]
};

class RconBridge {
    constructor() {
        this.rcon = null;
        this.config = null;
        this.commandQueue = [];
        this.isProcessing = false;
        this.prevBlocks = new Array(W * H * D).fill('air');
        this.throttle = { maxPerTick: 800, tps: 20 };
        
        // WebSocket server na portu 8787
        this.wss = new WebSocketServer({ port: 8787 });
        this.setupWebSocket();
        
        console.log('🚀 RCON Bridge server spuštěn na ws://localhost:8787');
    }

    setupWebSocket() {
        this.wss.on('connection', (ws) => {
            console.log('📱 Nové WebSocket připojení');
            
            ws.on('message', async (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    await this.handleMessage(ws, message);
                } catch (error) {
                    console.error('❌ Chyba při zpracování zprávy:', error);
                    ws.send(JSON.stringify({ type: 'error', message: error.message }));
                }
            });

            ws.on('close', () => {
                console.log('📱 WebSocket odpojeno');
            });
        });
    }

    async handleMessage(ws, message) {
        switch (message.type) {
            case 'config':
                await this.handleConfig(ws, message);
                break;
            case 'voxset':
                await this.handleVoxset(message.voxels);
                break;
            case 'clear_volume':
                await this.handleClearVolume();
                break;
            case 'clear_region':
                await this.handleClearRegion(message);
                break;
            default:
                throw new Error(`Neznámý typ zprávy: ${message.type}`);
        }
    }

    async handleConfig(ws, message) {
        this.config = {
            rcon: message.rcon,
            origin: message.origin,
            axes: message.axes,
            throttle: message.throttle || this.throttle
        };

        // Připojení k RCON
        try {
            if (this.rcon) {
                await this.rcon.end();
            }
            
            this.rcon = await Rcon.connect({
                host: this.config.rcon.host,
                port: this.config.rcon.port,
                password: this.config.rcon.password
            });
            
            console.log('🔗 RCON připojeno k Minecraft serveru');
            
            // Spuštění throttling smyčky
            this.startThrottling();
            
            ws.send(JSON.stringify({
                type: 'config_ack',
                ok: true,
                dims: { W, H, D }
            }));
            
        } catch (error) {
            console.error('❌ RCON připojení selhalo:', error);
            ws.send(JSON.stringify({
                type: 'config_ack',
                ok: false,
                error: error.message
            }));
        }
    }

    async handleVoxset(voxels) {
        for (const voxel of voxels) {
            const { x, y, z } = voxel;
            
            // Kontrola hranic
            if (x < 0 || x >= W || y < 0 || y >= H || z < 0 || z >= D) continue;
            
            let blockId;
            if (voxel.blockId) {
                blockId = voxel.blockId;
            } else if (voxel.r !== undefined && voxel.g !== undefined && voxel.b !== undefined) {
                blockId = this.rgbToConcrete(voxel.r, voxel.g, voxel.b);
            } else {
                continue;
            }
            
            // Cache kontrola
            const index = y * W * D + x * D + z;
            if (this.prevBlocks[index] === blockId) continue;
            
            this.prevBlocks[index] = blockId;
            
            // Přidání do fronty
            const worldPos = this.voxelToWorld(x, y, z);
            this.commandQueue.push({
                command: `setblock ${worldPos.x} ${worldPos.y} ${worldPos.z} ${blockId}`,
                priority: blockId === 'air' ? 1 : 0 // Air má vyšší prioritu
            });
        }
    }

    async handleClearVolume() {
        // Vymazání celého objemu
        const { x: x0, y: y0, z: z0 } = this.voxelToWorld(0, 0, 0);
        const { x: x1, y: y1, z: z1 } = this.voxelToWorld(W-1, H-1, D-1);
        
        this.commandQueue.push({
            command: `fill ${x0} ${y0} ${z0} ${x1} ${y1} ${z1} air`,
            priority: 2 // Nejvyšší priorita
        });
        
        // Vyčištění cache
        this.prevBlocks.fill('air');
        console.log('🧹 Objem vymazán');
    }

    async handleClearRegion(message) {
        const { x0, y0, z0, x1, y1, z1 } = message;
        const block = message.block || 'air';
        
        const world0 = this.voxelToWorld(x0, y0, z0);
        const world1 = this.voxelToWorld(x1, y1, z1);
        
        this.commandQueue.push({
            command: `fill ${world0.x} ${world0.y} ${world0.z} ${world1.x} ${world1.y} ${world1.z} ${block}`,
            priority: 2
        });
        
        // Aktualizace cache pro region
        for (let y = y0; y <= y1; y++) {
            for (let x = x0; x <= x1; x++) {
                for (let z = z0; z <= z1; z++) {
                    if (x >= 0 && x < W && y >= 0 && y < H && z >= 0 && z < D) {
                        const index = y * W * D + x * D + z;
                        this.prevBlocks[index] = block;
                    }
                }
            }
        }
        
        console.log(`🧹 Region vymazána: (${x0},${y0},${z0}) -> (${x1},${y1},${z1})`);
    }

    voxelToWorld(x, y, z) {
        const { origin, axes } = this.config;
        return {
            x: Math.round(origin.x + axes.right[0] * x + axes.down[0] * y + axes.forward[0] * z),
            y: Math.round(origin.y + axes.right[1] * x + axes.down[1] * y + axes.forward[1] * z),
            z: Math.round(origin.z + axes.right[2] * x + axes.down[2] * y + axes.forward[2] * z)
        };
    }

    rgbToConcrete(r, g, b) {
        let minDistance = Infinity;
        let closestBlock = 'white_concrete';
        
        for (const [blockName, [br, bg, bb]] of Object.entries(CONCRETE_COLORS)) {
            const distance = Math.sqrt((r - br) ** 2 + (g - bg) ** 2 + (b - bb) ** 2);
            if (distance < minDistance) {
                minDistance = distance;
                closestBlock = blockName;
            }
        }
        
        return closestBlock;
    }

    startThrottling() {
        if (this.isProcessing) return;
        this.isProcessing = true;
        
        const tickInterval = 1000 / this.config.throttle.tps;
        
        const processCommands = async () => {
            if (!this.rcon || this.commandQueue.length === 0) {
                setTimeout(processCommands, tickInterval);
                return;
            }
            
            // Seřazení podle priority (vyšší číslo = vyšší priorita)
            this.commandQueue.sort((a, b) => b.priority - a.priority);
            
            const commandsToProcess = this.commandQueue.splice(0, this.config.throttle.maxPerTick);
            
            for (const { command } of commandsToProcess) {
                try {
                    await this.rcon.send(command);
                } catch (error) {
                    console.error(`❌ RCON příkaz selhal: ${command}`, error.message);
                }
            }
            
            if (commandsToProcess.length > 0) {
                console.log(`📦 Zpracováno ${commandsToProcess.length} příkazů, zbývá ${this.commandQueue.length}`);
            }
            
            setTimeout(processCommands, tickInterval);
        };
        
        processCommands();
    }
}

// Spuštění serveru
new RconBridge();
