#!/usr/bin/env python3
"""
Zjednodušený server bez AI knihoven - jen pro testování systému
Přijímá snímky z mobilu a posílá testovací voxely do Minecraft
"""

import asyncio
import websockets
import json
import base64
import random
import time
import logging

# Konstanty konfigurace
W, H, D = 64, 36, 36
BRIDGE_URL = "ws://localhost:8787"
RCON_CFG = {"host": "127.0.0.1", "port": 25575, "password": "changeme"}
ORIGIN = {"x": 0, "y": 64, "z": 0}
AXES = {"right": [1, 0, 0], "down": [0, -1, 0], "forward": [0, 0, 1]}
THROTTLE = {"maxPerTick": 400, "tps": 20}

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleServer:
    def __init__(self):
        self.bridge_ws = None
        self.frame_count = 0
        self.last_update = time.time()
        self.demo_mode = True  # Režim ukázky bez AI
        
        logger.info("🚀 Zjednodušený server inicializován")
        
    async def connect_to_bridge(self):
        """Připojení k RCON bridge a odeslání konfigurace"""
        try:
            self.bridge_ws = await websockets.connect(BRIDGE_URL)
            logger.info("🔗 Připojeno k RCON bridge")
            
            # Odeslání konfigurace
            config_msg = {
                "type": "config",
                "rcon": RCON_CFG,
                "origin": ORIGIN,
                "axes": AXES,
                "throttle": THROTTLE
            }
            await self.bridge_ws.send(json.dumps(config_msg))
            
            # Čekání na potvrzení
            response = await self.bridge_ws.recv()
            config_ack = json.loads(response)
            
            if config_ack.get("ok"):
                logger.info("✅ Bridge konfigurace úspěšná")
                return True
            else:
                logger.error(f"❌ Bridge konfigurace selhala: {config_ack.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Připojení k bridge selhalo: {e}")
            return False
    
    def create_demo_voxels(self):
        """Vytvoří ukázkové voxely - animovanou kostku"""
        voxels = []
        
        # Animovaná kostka ve středu
        center_x, center_y, center_z = W//2, H//2, D//2
        size = 5 + int(3 * abs(time.time() % 4 - 2))  # Pulzující velikost
        
        # Barva se mění v čase
        hue = (time.time() * 50) % 360
        if hue < 120:
            r, g, b = int(255 * (120 - hue) / 120), int(255 * hue / 120), 0
        elif hue < 240:
            r, g, b = 0, int(255 * (240 - hue) / 120), int(255 * (hue - 120) / 120)
        else:
            r, g, b = int(255 * (hue - 240) / 120), 0, int(255 * (360 - hue) / 120)
        
        # Vytvoření kostky
        for x in range(max(0, center_x - size), min(W, center_x + size)):
            for y in range(max(0, center_y - size), min(H, center_y + size)):
                for z in range(max(0, center_z - size), min(D, center_z + size)):
                    # Jen okraje kostky
                    if (x == center_x - size or x == center_x + size - 1 or
                        y == center_y - size or y == center_y + size - 1 or
                        z == center_z - size or z == center_z + size - 1):
                        
                        voxels.append({
                            "x": x, "y": y, "z": z,
                            "r": r, "g": g, "b": b
                        })
        
        return voxels
    
    def create_person_simulation(self):
        """Simuluje jednoduchou postavu"""
        voxels = []
        
        # Základní parametry postavy
        base_x = W // 2
        base_y = H - 10
        base_z = D // 2
        
        # Pohyb postavy
        offset_x = int(5 * (time.time() % 8 - 4))  # Pohyb zleva doprava
        
        # Hlava
        head_x = base_x + offset_x
        for y in range(base_y - 8, base_y - 5):
            for x in range(head_x - 2, head_x + 3):
                for z in range(base_z - 2, base_z + 3):
                    if 0 <= x < W and 0 <= y < H and 0 <= z < D:
                        voxels.append({
                            "x": x, "y": y, "z": z,
                            "r": 255, "g": 220, "b": 177  # Barva kůže
                        })
        
        # Tělo
        for y in range(base_y - 5, base_y):
            for x in range(head_x - 1, head_x + 2):
                for z in range(base_z - 1, base_z + 2):
                    if 0 <= x < W and 0 <= y < H and 0 <= z < D:
                        voxels.append({
                            "x": x, "y": y, "z": z,
                            "r": 0, "g": 100, "b": 200  # Modrá košile
                        })
        
        # Nohy
        leg_animation = int(time.time() * 2) % 2  # Střídání nohou
        for leg in range(2):
            leg_x = head_x + (leg * 2 - 1)  # Levá a pravá noha
            leg_offset = leg_animation if leg == 0 else 1 - leg_animation
            
            for y in range(base_y, base_y + 3):
                for z in range(base_z - leg_offset, base_z + 1):
                    if 0 <= leg_x < W and 0 <= y < H and 0 <= z < D:
                        voxels.append({
                            "x": leg_x, "y": y, "z": z,
                            "r": 50, "g": 50, "b": 50  # Černé kalhoty
                        })
        
        return voxels
    
    async def process_frame(self, jpg_data: str):
        """Zpracování snímku - v demo módu vytvoří testovací data"""
        self.frame_count += 1
        
        if self.demo_mode:
            # Demo režim - animované voxely
            if self.frame_count % 10 == 1:  # Každý 10. frame vymaž
                clear_msg = {"type": "clear_volume"}
                await self.bridge_ws.send(json.dumps(clear_msg))
                logger.info("🧹 Prostor vymazán")
                
            # Vytvoř nové voxely
            if self.frame_count % 5 == 0:  # Každý 5. frame
                voxels = self.create_person_simulation()
                
                if voxels:
                    voxset_msg = {
                        "type": "voxset",
                        "voxels": voxels
                    }
                    await self.bridge_ws.send(json.dumps(voxset_msg))
                    logger.info(f"📦 Odesláno {len(voxels)} voxelů (simulace postavy)")
        else:
            # Skutečné zpracování by bylo zde
            logger.info(f"📸 Přijat snímek #{self.frame_count} (AI zpracování vypnuto)")
    
    async def handle_mobile_client(self, websocket, path):
        """Handler pro připojení mobilního klienta"""
        logger.info("📱 Mobilní klient připojen")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    if data.get("type") == "frame":
                        jpg_data = data.get("jpg")
                        if jpg_data and self.bridge_ws:
                            await self.process_frame(jpg_data)
                        
                        # Pošli potvrzení zpět
                        await websocket.send(json.dumps({
                            "type": "ack",
                            "frame_count": self.frame_count,
                            "demo_mode": self.demo_mode
                        }))
                            
                except json.JSONDecodeError:
                    logger.warning("⚠️ Neplatná JSON zpráva od klienta")
                except Exception as e:
                    logger.error(f"❌ Chyba při zpracování zprávy: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("📱 Mobilní klient odpojen")
        except Exception as e:
            logger.error(f"❌ Chyba v komunikaci s klientem: {e}")
    
    async def start_server(self):
        """Spuštění WebSocket serveru"""
        if not await self.connect_to_bridge():
            logger.error("❌ Nelze se připojit k bridge - ukončuji")
            return
        
        logger.info("🚀 Spouštím WebSocket server na ws://0.0.0.0:9090")
        logger.info("🎭 Demo režim aktivní - simulace postavy bez AI")
        logger.info("💡 Pro vypnutí demo módu změňte self.demo_mode = False")
        
        async with websockets.serve(self.handle_mobile_client, "0.0.0.0", 9090):
            logger.info("✅ Server běží, čekám na připojení...")
            
            # Spuštění demo animace
            asyncio.create_task(self.demo_animation())
            
            await asyncio.Future()  # Běž navždy
    
    async def demo_animation(self):
        """Spouští demo animaci i bez mobilního klienta"""
        while True:
            if self.demo_mode and self.bridge_ws:
                try:
                    await self.process_frame("")  # Prázdný frame pro demo
                    await asyncio.sleep(0.5)  # 2 FPS pro demo
                except Exception as e:
                    logger.error(f"❌ Chyba v demo animaci: {e}")
                    await asyncio.sleep(1)
            else:
                await asyncio.sleep(1)

if __name__ == "__main__":
    server = SimpleServer()
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("🛑 Server zastaven uživatelem")
    except Exception as e:
        logger.error(f"❌ Kritická chyba serveru: {e}")
