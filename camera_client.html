<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Kamera Mód - Mobilní Klient</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 30px;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select, button {
            width: 100%;
            padding: 12px;
            border: 1px solid #555;
            border-radius: 5px;
            background: #333;
            color: white;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: #4CAF50;
            cursor: pointer;
            margin-top: 10px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .stop-btn {
            background: #f44336;
        }
        
        .stop-btn:hover {
            background: #da190b;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.disconnected {
            background: #f44336;
        }
        
        .status.connected {
            background: #4CAF50;
        }
        
        .status.connecting {
            background: #ff9800;
        }
        
        #videoPreview {
            width: 100%;
            max-width: 320px;
            height: auto;
            border: 2px solid #555;
            border-radius: 5px;
            margin: 20px auto;
            display: block;
        }
        
        .stats {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        
        .stats h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 3D Kamera Mód</h1>
        
        <div class="control-group">
            <label for="wsUrl">WebSocket URL serveru:</label>
            <input type="text" id="wsUrl" value="ws://*************:9090" placeholder="ws://IP_ADRESA:9090">
        </div>
        
        <div class="control-group">
            <label for="fps">FPS (snímky za sekundu):</label>
            <input type="range" id="fps" min="1" max="30" value="10">
            <span id="fpsValue">10</span>
        </div>
        
        <div class="control-group">
            <label for="resolution">Rozlišení:</label>
            <select id="resolution">
                <option value="320x180">320×180 (rychlé)</option>
                <option value="640x360" selected>640×360 (vyvážené)</option>
                <option value="1280x720">1280×720 (kvalitní)</option>
            </select>
        </div>
        
        <div class="control-group">
            <label for="quality">JPEG kvalita:</label>
            <input type="range" id="quality" min="0.1" max="1.0" step="0.1" value="0.8">
            <span id="qualityValue">0.8</span>
        </div>
        
        <div id="status" class="status disconnected">Odpojeno</div>
        
        <button id="startBtn">🎥 Spustit kameru</button>
        <button id="stopBtn" class="stop-btn" disabled>⏹️ Zastavit</button>
        
        <video id="videoPreview" autoplay muted playsinline style="display: none;"></video>
        
        <div class="stats">
            <h3>📊 Statistiky</h3>
            <div class="stat-item">
                <span>Stav připojení:</span>
                <span id="connectionState">Odpojeno</span>
            </div>
            <div class="stat-item">
                <span>Odesláno snímků:</span>
                <span id="framesSent">0</span>
            </div>
            <div class="stat-item">
                <span>Aktuální FPS:</span>
                <span id="actualFps">0</span>
            </div>
            <div class="stat-item">
                <span>Velikost snímku:</span>
                <span id="frameSize">0 KB</span>
            </div>
        </div>
    </div>

    <script>
        class CameraClient {
            constructor() {
                this.ws = null;
                this.video = document.getElementById('videoPreview');
                this.canvas = document.createElement('canvas');
                this.ctx = this.canvas.getContext('2d');
                this.stream = null;
                this.isRunning = false;
                this.frameInterval = null;
                this.framesSent = 0;
                this.lastFrameTime = 0;
                this.fpsCounter = 0;
                this.fpsTimer = null;
                
                this.setupEventListeners();
                this.updateFpsDisplay();
                this.updateQualityDisplay();
            }
            
            setupEventListeners() {
                // FPS slider
                document.getElementById('fps').addEventListener('input', (e) => {
                    this.updateFpsDisplay();
                    if (this.isRunning) {
                        this.restartFrameCapture();
                    }
                });
                
                // Quality slider
                document.getElementById('quality').addEventListener('input', (e) => {
                    this.updateQualityDisplay();
                });
                
                // Tlačítka
                document.getElementById('startBtn').addEventListener('click', () => this.start());
                document.getElementById('stopBtn').addEventListener('click', () => this.stop());
            }
            
            updateFpsDisplay() {
                const fps = document.getElementById('fps').value;
                document.getElementById('fpsValue').textContent = fps;
            }
            
            updateQualityDisplay() {
                const quality = document.getElementById('quality').value;
                document.getElementById('qualityValue').textContent = quality;
            }
            
            updateStatus(message, className) {
                const statusEl = document.getElementById('status');
                statusEl.textContent = message;
                statusEl.className = `status ${className}`;
                document.getElementById('connectionState').textContent = message;
            }
            
            async connectWebSocket() {
                const wsUrl = document.getElementById('wsUrl').value;
                
                try {
                    this.updateStatus('Připojování...', 'connecting');
                    this.ws = new WebSocket(wsUrl);
                    
                    this.ws.onopen = () => {
                        this.updateStatus('Připojeno', 'connected');
                        console.log('✅ WebSocket připojen');
                    };
                    
                    this.ws.onclose = () => {
                        this.updateStatus('Odpojeno', 'disconnected');
                        console.log('📱 WebSocket odpojen');
                    };
                    
                    this.ws.onerror = (error) => {
                        this.updateStatus('Chyba připojení', 'disconnected');
                        console.error('❌ WebSocket chyba:', error);
                    };
                    
                    // Čekání na připojení
                    await new Promise((resolve, reject) => {
                        this.ws.onopen = resolve;
                        this.ws.onerror = reject;
                        setTimeout(reject, 5000); // Timeout 5s
                    });
                    
                } catch (error) {
                    this.updateStatus('Připojení selhalo', 'disconnected');
                    throw error;
                }
            }
            
            async startCamera() {
                const resolution = document.getElementById('resolution').value;
                const [width, height] = resolution.split('x').map(Number);
                
                try {
                    this.stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            facingMode: 'environment', // Zadní kamera
                            width: { ideal: width },
                            height: { ideal: height }
                        }
                    });
                    
                    this.video.srcObject = this.stream;
                    this.video.style.display = 'block';
                    
                    // Nastavení canvas rozměrů
                    this.canvas.width = width;
                    this.canvas.height = height;
                    
                    console.log(`📹 Kamera spuštěna: ${width}×${height}`);
                    
                } catch (error) {
                    console.error('❌ Chyba při spuštění kamery:', error);
                    alert('Nepodařilo se spustit kameru. Zkontrolujte oprávnění.');
                    throw error;
                }
            }
            
            startFrameCapture() {
                const fps = parseInt(document.getElementById('fps').value);
                const interval = 1000 / fps;
                
                this.frameInterval = setInterval(() => {
                    this.captureAndSendFrame();
                }, interval);
                
                // FPS počítadlo
                this.fpsTimer = setInterval(() => {
                    document.getElementById('actualFps').textContent = this.fpsCounter;
                    this.fpsCounter = 0;
                }, 1000);
            }
            
            restartFrameCapture() {
                if (this.frameInterval) {
                    clearInterval(this.frameInterval);
                    this.startFrameCapture();
                }
            }
            
            captureAndSendFrame() {
                if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;
                
                try {
                    // Kreslení video frame do canvas
                    this.ctx.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
                    
                    // Konverze na JPEG
                    const quality = parseFloat(document.getElementById('quality').value);
                    const dataUrl = this.canvas.toDataURL('image/jpeg', quality);
                    const base64Data = dataUrl.split(',')[1];
                    
                    // Odeslání přes WebSocket
                    const message = {
                        type: 'frame',
                        jpg: base64Data
                    };
                    
                    this.ws.send(JSON.stringify(message));
                    
                    // Statistiky
                    this.framesSent++;
                    this.fpsCounter++;
                    document.getElementById('framesSent').textContent = this.framesSent;
                    
                    // Velikost snímku
                    const sizeKB = Math.round(base64Data.length * 0.75 / 1024); // Přibližná velikost
                    document.getElementById('frameSize').textContent = `${sizeKB} KB`;
                    
                } catch (error) {
                    console.error('❌ Chyba při zachycení snímku:', error);
                }
            }
            
            async start() {
                try {
                    document.getElementById('startBtn').disabled = true;
                    
                    await this.connectWebSocket();
                    await this.startCamera();
                    
                    this.startFrameCapture();
                    this.isRunning = true;
                    
                    document.getElementById('stopBtn').disabled = false;
                    console.log('🚀 Kamera mód spuštěn');
                    
                } catch (error) {
                    console.error('❌ Chyba při spuštění:', error);
                    this.stop();
                }
            }
            
            stop() {
                this.isRunning = false;
                
                // Zastavení zachycování snímků
                if (this.frameInterval) {
                    clearInterval(this.frameInterval);
                    this.frameInterval = null;
                }
                
                if (this.fpsTimer) {
                    clearInterval(this.fpsTimer);
                    this.fpsTimer = null;
                }
                
                // Zastavení kamery
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                    this.video.style.display = 'none';
                }
                
                // Zavření WebSocket
                if (this.ws) {
                    this.ws.close();
                    this.ws = null;
                }
                
                // Reset UI
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                this.updateStatus('Odpojeno', 'disconnected');
                
                console.log('🛑 Kamera mód zastaven');
            }
        }
        
        // Inicializace při načtení stránky
        document.addEventListener('DOMContentLoaded', () => {
            new CameraClient();
        });
    </script>
</body>
</html>
