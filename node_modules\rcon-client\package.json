{"name": "rcon-client", "version": "4.2.5", "description": "A modern RCON client made for Minecraft that features a packet queue", "main": "lib", "typings": "lib", "scripts": {"prepare": "npm run build && npm run test", "build": "tsc", "test": "jasmine"}, "repository": {"type": "git", "url": "git+https://github.com/janispritzkau/rcon-client.git"}, "keywords": ["rcon", "client", "promise", "typescript", "minecraft", "protocol"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"typed-emitter": "^0.1.0"}, "devDependencies": {"@types/node": "^10.14.20", "jasmine": "^3.5.0", "typescript": "^3.6.3"}}