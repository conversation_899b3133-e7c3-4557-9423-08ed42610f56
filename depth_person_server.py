#!/usr/bin/env python3
"""
Depth + Person segmentation server pro 3D voxel rendering
Přijímá snímky z mobilu, zpracovává segmentaci a <PERSON>lou<PERSON>ku, posílá voxely do MC bridge
"""

import asyncio
import websockets
import json
import base64
import cv2
import numpy as np
import torch
import mediapipe as mp
from typing import Optional, Tuple, Dict, List
import logging

# Konstanty konfigurace
W, H, D = 64, 36, 36
MASK_THR = 0.5
THICK = 2
SMOOTH = 0.15
NEAR, FAR = 0.30, 2.70
BRIDGE_URL = "ws://localhost:8787"
RCON_CFG = {"host": "127.0.0.1", "port": 25575, "password": "changeme"}
ORIGIN = {"x": 0, "y": 64, "z": 0}
AXES = {"right": [1, 0, 0], "down": [0, -1, 0], "forward": [0, 0, 1]}
THROTTLE = {"maxPerTick": 800, "tps": 20}

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DepthPersonServer:
    def __init__(self):
        self.device = self.detect_device()
        self.midas_model = None
        self.midas_transform = None
        self.mp_selfie = None
        self.selfie_segmentation = None
        self.bridge_ws = None
        self.prev_occupancy = np.zeros((H, W, D), dtype=bool)
        self.prev_depth_smooth = None
        
        logger.info(f"🚀 Server inicializován na zařízení: {self.device}")
        
    def detect_device(self) -> str:
        """Detekce dostupného zařízení pro inference"""
        if torch.cuda.is_available():
            device = "cuda"
            logger.info(f"✅ CUDA dostupná: {torch.cuda.get_device_name()}")
        else:
            device = "cpu"
            logger.info("⚠️ CUDA nedostupná, používám CPU")
        return device
    
    async def initialize_models(self):
        """Inicializace AI modelů"""
        logger.info("🔄 Načítání modelů...")
        
        # MiDaS pro monokulární hloubku
        try:
            self.midas_model = torch.hub.load("intel-isl/MiDaS", "DPT_Small")
            self.midas_model.to(self.device)
            self.midas_model.eval()
            
            midas_transforms = torch.hub.load("intel-isl/MiDaS", "transforms")
            self.midas_transform = midas_transforms.dpt_transform
            logger.info("✅ MiDaS model načten")
        except Exception as e:
            logger.error(f"❌ Chyba při načítání MiDaS: {e}")
            raise
        
        # MediaPipe pro segmentaci osoby
        try:
            self.mp_selfie = mp.solutions.selfie_segmentation
            self.selfie_segmentation = self.mp_selfie.SelfieSegmentation(model_selection=1)
            logger.info("✅ MediaPipe segmentace načtena")
        except Exception as e:
            logger.error(f"❌ Chyba při načítání MediaPipe: {e}")
            raise
    
    async def connect_to_bridge(self):
        """Připojení k RCON bridge a odeslání konfigurace"""
        try:
            self.bridge_ws = await websockets.connect(BRIDGE_URL)
            logger.info("🔗 Připojeno k RCON bridge")
            
            # Odeslání konfigurace
            config_msg = {
                "type": "config",
                "rcon": RCON_CFG,
                "origin": ORIGIN,
                "axes": AXES,
                "throttle": THROTTLE
            }
            await self.bridge_ws.send(json.dumps(config_msg))
            
            # Čekání na potvrzení
            response = await self.bridge_ws.recv()
            config_ack = json.loads(response)
            
            if config_ack.get("ok"):
                logger.info("✅ Bridge konfigurace úspěšná")
            else:
                logger.error(f"❌ Bridge konfigurace selhala: {config_ack.get('error')}")
                
        except Exception as e:
            logger.error(f"❌ Připojení k bridge selhalo: {e}")
            raise
    
    def process_frame(self, jpg_data: str) -> Optional[List[Dict]]:
        """Zpracování jednoho snímku - segmentace + hloubka -> voxely"""
        try:
            # Dekódování JPEG z base64
            img_bytes = base64.b64decode(jpg_data)
            img_array = np.frombuffer(img_bytes, dtype=np.uint8)
            img_bgr = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            
            if img_bgr is None:
                logger.warning("⚠️ Nepodařilo se dekódovat snímek")
                return None
            
            img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
            
            # Segmentace osoby pomocí MediaPipe
            results = self.selfie_segmentation.process(img_rgb)
            mask = results.segmentation_mask
            
            # Monokulární hloubka pomocí MiDaS
            input_tensor = self.midas_transform(img_rgb).to(self.device)
            
            with torch.no_grad():
                depth_raw = self.midas_model(input_tensor.unsqueeze(0))
                depth_raw = depth_raw.squeeze().cpu().numpy()
            
            # Normalizace hloubky na 0-1 (větší hodnota = blíž)
            depth_norm = (depth_raw - depth_raw.min()) / (depth_raw.max() - depth_raw.min())
            depth_norm = 1.0 - depth_norm  # Inverze pro správnou orientaci
            
            # Exponenciální vyhlazení hloubky
            if self.prev_depth_smooth is not None:
                depth_norm = SMOOTH * depth_norm + (1 - SMOOTH) * self.prev_depth_smooth
            self.prev_depth_smooth = depth_norm.copy()
            
            # Downsample na cílové rozlišení W×H
            mask_small = cv2.resize(mask, (W, H), interpolation=cv2.INTER_AREA)
            depth_small = cv2.resize(depth_norm, (W, H), interpolation=cv2.INTER_AREA)
            img_small = cv2.resize(img_rgb, (W, H), interpolation=cv2.INTER_AREA)
            
            # Lehký blur pro vyhlazení
            mask_small = cv2.GaussianBlur(mask_small, (3, 3), 0.5)
            depth_small = cv2.GaussianBlur(depth_small, (3, 3), 0.5)
            
            # Mapování hloubky na Z souřadnici
            depth_z = np.clip((depth_small - NEAR) / (FAR - NEAR), 0, 1)
            depth_z = np.round(depth_z * (D - 1)).astype(int)
            
            # Voxelizace - vytvoření 3D obsazenosti
            new_occupancy = np.zeros((H, W, D), dtype=bool)
            
            for y in range(H):
                for x in range(W):
                    if mask_small[y, x] >= MASK_THR:
                        z_center = depth_z[y, x]
                        z_min = max(0, z_center - THICK)
                        z_max = min(D - 1, z_center + THICK)
                        
                        for z in range(z_min, z_max + 1):
                            new_occupancy[y, x, z] = True
            
            # Diff algoritmus - najít změny
            voxel_changes = []
            
            # Co zmizelo -> air
            disappeared = self.prev_occupancy & ~new_occupancy
            for y, x, z in np.argwhere(disappeared):
                voxel_changes.append({"x": int(x), "y": int(y), "z": int(z), "blockId": "air"})
            
            # Co přibylo -> barevné bloky
            appeared = new_occupancy & ~self.prev_occupancy
            for y, x, z in np.argwhere(appeared):
                # Barva z původního snímku s lehkým zesvětlením ve středu
                r, g, b = img_small[y, x]
                
                # Zesvětlení ve středu sloupku
                z_center = depth_z[y, x]
                if abs(z - z_center) <= 1:
                    brightness_boost = 1.2
                    r = min(255, int(r * brightness_boost))
                    g = min(255, int(g * brightness_boost))
                    b = min(255, int(b * brightness_boost))
                
                voxel_changes.append({
                    "x": int(x), "y": int(y), "z": int(z),
                    "r": int(r), "g": int(g), "b": int(b)
                })
            
            # Uložení stavu pro příští frame
            self.prev_occupancy = new_occupancy.copy()
            
            logger.info(f"📊 Zpracován frame: {len(voxel_changes)} změn, maska {np.sum(mask_small >= MASK_THR)} pixelů")
            return voxel_changes
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování frame: {e}")
            return None
    
    async def handle_mobile_client(self, websocket, path):
        """Handler pro připojení mobilního klienta"""
        logger.info("📱 Mobilní klient připojen")
        
        try:
            async for message in websocket:
                try:
                    data = json.loads(message)
                    
                    if data.get("type") == "frame":
                        jpg_data = data.get("jpg")
                        if jpg_data:
                            voxel_changes = self.process_frame(jpg_data)
                            
                            if voxel_changes and self.bridge_ws:
                                # Odeslání změn do bridge
                                voxset_msg = {
                                    "type": "voxset",
                                    "voxels": voxel_changes
                                }
                                await self.bridge_ws.send(json.dumps(voxset_msg))
                                
                except json.JSONDecodeError:
                    logger.warning("⚠️ Neplatná JSON zpráva od klienta")
                except Exception as e:
                    logger.error(f"❌ Chyba při zpracování zprávy: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("📱 Mobilní klient odpojen")
        except Exception as e:
            logger.error(f"❌ Chyba v komunikaci s klientem: {e}")
    
    async def start_server(self):
        """Spuštění WebSocket serveru"""
        await self.initialize_models()
        await self.connect_to_bridge()
        
        logger.info("🚀 Spouštím WebSocket server na ws://0.0.0.0:9090")
        
        async with websockets.serve(self.handle_mobile_client, "0.0.0.0", 9090):
            logger.info("✅ Server běží, čekám na připojení...")
            await asyncio.Future()  # Běž navždy

if __name__ == "__main__":
    server = DepthPersonServer()
    try:
        asyncio.run(server.start_server())
    except KeyboardInterrupt:
        logger.info("🛑 Server zastaven uživatelem")
    except Exception as e:
        logger.error(f"❌ Kritická chyba serveru: {e}")
